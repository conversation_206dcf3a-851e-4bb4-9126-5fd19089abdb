{"buildCommand": "npm run build:full", "outputDirectory": "dist", "installCommand": "npm ci", "framework": null, "rewrites": [{"source": "/sitemap.xml", "destination": "/sitemap.xml"}, {"source": "/robots.txt", "destination": "/robots.txt"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/sitemap.xml", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}, {"key": "Content-Type", "value": "application/xml"}]}, {"source": "/robots.txt", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}, {"key": "Content-Type", "value": "text/plain"}]}, {"source": "/llms.txt", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}, {"key": "Content-Type", "value": "text/plain"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}]}