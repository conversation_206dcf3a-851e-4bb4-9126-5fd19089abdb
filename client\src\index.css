@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  
  /* Custom variables for futuristic theme */
  --dark-primary: hsl(240, 35%, 7%);
  --dark-secondary: hsl(243, 23%, 11%);
  --neon-blue: hsl(194, 100%, 50%);
  --neon-purple: hsl(277, 54%, 60%);
  --glass-bg: hsla(0, 0%, 100%, 0.1);
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    overflow-x: hidden;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
  }

  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  /* Custom scrollbar for Firefox */
  html {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) rgba(0, 0, 0, 0.1);
  }

  /* Prevent layout shift during content loading */
  * {
    box-sizing: border-box;
  }
}

@layer utilities {
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid hsla(0, 0%, 100%, 0.2);
  }
  
  .neon-glow {
    box-shadow: 0 0 20px hsla(194, 100%, 50%, 0.3);
  }
  
  .neon-glow:hover {
    box-shadow: 0 0 30px hsla(194, 100%, 50%, 0.5);
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, var(--dark-primary) 0%, var(--dark-secondary) 50%, hsl(220, 26%, 14%) 100%);
  }
  
  .particle-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 80%, hsla(194, 100%, 50%, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, hsla(277, 54%, 60%, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, hsla(258, 100%, 50%, 0.1) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  .idea-card {
    transition: background-color 0.2s ease;
    will-change: auto;
  }

  /* Prevent layout shift and page jumping */
  .stable-layout {
    contain: layout style;
  }

  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Optimize rendering performance */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
  
  .loading-skeleton {
    background: linear-gradient(90deg, hsla(0, 0%, 100%, 0.1) 25%, hsla(0, 0%, 100%, 0.2) 50%, hsla(0, 0%, 100%, 0.1) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
  
  .stats-counter {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-neon-blue {
    color: var(--neon-blue);
  }
  
  .text-neon-purple {
    color: var(--neon-purple);
  }
  
  .bg-neon-blue\/20 {
    background-color: hsla(194, 100%, 50%, 0.2);
  }
  
  .bg-neon-purple\/20 {
    background-color: hsla(277, 54%, 60%, 0.2);
  }
  
  .border-neon-blue\/30 {
    border-color: hsla(194, 100%, 50%, 0.3);
  }
  
  .focus\:border-neon-blue:focus {
    border-color: var(--neon-blue);
  }
  
  .focus\:ring-neon-blue:focus {
    ring-color: var(--neon-blue);
  }

  /* Industry colors */
  .text-industry-neon-blue { color: #22d3ee !important; }
  .text-industry-neon-blue-selected { color: #bfdbfe !important; }
  .text-industry-neon-purple { color: #a855f7 !important; }
  .text-industry-neon-purple-selected { color: #ddd6fe !important; }
  .text-industry-violet-400 { color: #8b5cf6 !important; }
  .text-industry-violet-400-selected { color: #ddd6fe !important; }
  .text-industry-green-400 { color: #4ade80 !important; }
  .text-industry-green-400-selected { color: #bbf7d0 !important; }
  .text-industry-yellow-400 { color: #facc15 !important; }
  .text-industry-yellow-400-selected { color: #fef3c7 !important; }
  .text-industry-orange-400 { color: #fb923c !important; }
  .text-industry-orange-400-selected { color: #fed7aa !important; }
  .text-industry-blue-400 { color: #60a5fa !important; }
  .text-industry-blue-400-selected { color: #bfdbfe !important; }
  .text-industry-pink-400 { color: #f472b6 !important; }
  .text-industry-pink-400-selected { color: #fce7f3 !important; }
  .text-industry-indigo-400 { color: #818cf8 !important; }
  .text-industry-indigo-400-selected { color: #e0e7ff !important; }
  .text-industry-red-400 { color: #f87171 !important; }
  .text-industry-red-400-selected { color: #fecaca !important; }
  .text-industry-cyan-400 { color: #22d3ee !important; }
  .text-industry-cyan-400-selected { color: #bfdbfe !important; }
  .text-industry-purple-400 { color: #c084fc !important; }
  .text-industry-purple-400-selected { color: #e9d5ff !important; }
  .text-industry-emerald-400 { color: #34d399 !important; }
  .text-industry-emerald-400-selected { color: #a7f3d0 !important; }

  /* Mobile responsive utilities */
  @media (max-width: 767px) {
    /* Ensure mobile menu button is always visible */
    .mobile-menu-button {
      position: fixed !important;
      top: 1rem !important;
      left: 1rem !important;
      z-index: 60 !important;
    }
    
    /* Mobile sidebar */
    .mobile-sidebar {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      height: 100vh !important;
      width: 320px !important;
      max-width: 85vw !important;
      z-index: 50 !important;
      overflow-y: auto !important;
    }
    
    /* Mobile main content */
    .mobile-main {
      width: 100% !important;
      padding-top: 4rem !important;
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }
    
    /* Prevent body scroll when mobile menu is open */
    .mobile-menu-open {
      overflow: hidden !important;
    }
    
    /* Optimize idea grid for mobile */
    .idea-grid {
      grid-template-columns: 1fr !important;
      gap: 1rem !important;
    }
    
    /* Mobile friendly buttons */
    .mobile-button {
      padding: 0.75rem 1rem !important;
      font-size: 0.875rem !important;
      width: 100% !important;
    }
    
    /* Mobile search filters */
    .mobile-filters {
      flex-direction: column !important;
      gap: 0.75rem !important;
    }
    
    /* Mobile stats */
    .mobile-stats {
      grid-template-columns: 1fr !important;
      gap: 1rem !important;
    }
  }
  
  /* Smooth transitions for mobile interactions */
  .mobile-transition {
    transition: transform 0.3s ease-in-out !important;
  }
  
  /* Mobile overlay */
  .mobile-overlay {
    position: fixed !important;
    inset: 0 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 40 !important;
  }
}
