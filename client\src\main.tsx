import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import React from 'react';
import { initPerformanceMonitoring } from './lib/analytics';

// Add error boundary and logging
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ 
          padding: '20px', 
          backgroundColor: '#1a1a1a', 
          color: 'white', 
          fontFamily: 'monospace',
          minHeight: '100vh'
        }}>
          <h1>Something went wrong.</h1>
          <details style={{ marginTop: '20px' }}>
            <summary>Error details</summary>
            <pre style={{ marginTop: '10px', color: '#ff6b6b' }}>
              {this.state.error?.toString()}
              {this.state.error?.stack}
            </pre>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

// Add global error handler
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// Initialize performance monitoring
initPerformanceMonitoring();

try {
  createRoot(document.getElementById("root")!).render(
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  );
} catch (error) {
  console.error('Failed to render app:', error);
  document.body.innerHTML = `
    <div style="padding: 20px; background: #1a1a1a; color: white; font-family: monospace;">
      <h1>Failed to start application</h1>
      <pre style="color: #ff6b6b; margin-top: 20px;">${error}</pre>
    </div>
  `;
}
