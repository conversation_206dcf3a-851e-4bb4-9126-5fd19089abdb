import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, Zap, Users, Star, ExternalLink, CheckCircle, XCircle, AlertCircle, BookOpen } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth.tsx";
import { useIdeas } from "@/hooks/use-ideas";
import SEOHead from "@/components/seo-head";
import ParticleBackground from "@/components/particle-background";
import IdeaGrid from "@/components/idea-grid";

// Alternative comparison configurations
const ALTERNATIVE_CONFIGS = {
  'chatgpt-vs-auramind-ai': {
    title: 'ChatGPT vs AuraMind AI: Memory Management Comparison',
    description: 'Compare ChatGPT and AuraMind AI for persistent memory and persona management. Find the best AI tool for long-term conversations.',
    tool1: {
      name: 'ChatGPT',
      description: 'Popular AI chatbot by OpenAI',
      pros: ['Wide knowledge base', 'Natural conversations', 'Multiple use cases', 'Large user community'],
      cons: ['No memory between sessions', 'No persistent personas', 'Limited customization', 'Generic responses'],
      pricing: 'Free / $20/month',
      rating: 4.2
    },
    tool2: {
      name: 'AuraMind AI',
      description: 'Specialized AI with persistent memory and personas',
      pros: ['Persistent memory', 'Custom personas', 'Long-term relationships', 'Specialized for memory'],
      cons: ['Newer platform', 'Smaller user base', 'Limited general knowledge', 'Higher learning curve'],
      pricing: 'Starting at $15/month',
      rating: 4.7
    },
    relatedKeywords: ['AI memory', 'LLM persona management', 'conversational AI'],
    searchKeywords: ['chatgpt alternative', 'ai memory management', 'persistent ai personas']
  },
  'notion-vs-competitors': {
    title: 'Notion vs Competitors: Best Productivity Tools 2025',
    description: 'Compare Notion with top alternatives for productivity, note-taking, and project management. Find the best tool for your workflow.',
    tool1: {
      name: 'Notion',
      description: 'All-in-one workspace for notes and projects',
      pros: ['Flexible database system', 'Rich formatting', 'Team collaboration', 'Template marketplace'],
      cons: ['Steep learning curve', 'Can be slow', 'Complex for simple tasks', 'Expensive for teams'],
      pricing: 'Free / $8-16/month',
      rating: 4.4
    },
    tool2: {
      name: 'Alternatives',
      description: 'Various specialized productivity tools',
      pros: ['Specialized features', 'Better performance', 'Simpler interfaces', 'Cost-effective options'],
      cons: ['Less flexibility', 'Multiple tools needed', 'Learning multiple systems', 'Integration challenges'],
      pricing: 'Varies by tool',
      rating: 4.3
    },
    relatedKeywords: ['productivity', 'note-taking', 'project management'],
    searchKeywords: ['notion alternative', 'productivity tools', 'note taking apps']
  },
  'traditional-vs-ai-tools': {
    title: 'Traditional Tools vs AI-Powered Alternatives',
    description: 'Compare traditional business tools with AI-powered alternatives. Discover how AI is transforming productivity and efficiency.',
    tool1: {
      name: 'Traditional Tools',
      description: 'Conventional business and productivity software',
      pros: ['Proven reliability', 'Familiar interfaces', 'Stable features', 'Lower learning curve'],
      cons: ['Manual processes', 'Limited automation', 'No intelligent insights', 'Time-consuming tasks'],
      pricing: 'Varies widely',
      rating: 3.8
    },
    tool2: {
      name: 'AI-Powered Tools',
      description: 'Next-generation tools with AI capabilities',
      pros: ['Intelligent automation', 'Predictive insights', 'Adaptive learning', 'Efficiency gains'],
      cons: ['Higher costs', 'Learning curve', 'Dependency on data', 'Potential errors'],
      pricing: 'Premium pricing',
      rating: 4.5
    },
    relatedKeywords: ['AI automation', 'business intelligence', 'productivity'],
    searchKeywords: ['ai tools vs traditional', 'ai automation benefits', 'ai productivity tools']
  },
  'linktree-vs-intelligent-alternatives': {
    title: 'Linktree vs Intelligent Link-in-Bio Tools',
    description: 'Compare Linktree with next-generation link-in-bio tools that offer analytics, customization, and monetization features.',
    tool1: {
      name: 'Linktree',
      description: 'Popular link-in-bio tool for social media',
      pros: ['Easy to use', 'Free tier available', 'Wide adoption', 'Simple setup'],
      cons: ['Limited customization', 'Basic analytics', 'No monetization features', 'Generic appearance'],
      pricing: 'Free / $5-24/month',
      rating: 4.1
    },
    tool2: {
      name: 'Intelligent Alternatives',
      description: 'Advanced link-in-bio tools with AI features',
      pros: ['Advanced analytics', 'Custom branding', 'Monetization tools', 'AI optimization'],
      cons: ['Higher cost', 'More complex setup', 'Smaller user base', 'Learning curve'],
      pricing: '$10-50/month',
      rating: 4.6
    },
    relatedKeywords: ['link-in-bio', 'creator monetization', 'social media tools'],
    searchKeywords: ['linktree alternative', 'link in bio tools', 'creator monetization']
  },
  'slack-vs-discord-business': {
    title: 'Slack vs Discord for Business Communication',
    description: 'Compare Slack and Discord for business team communication. Find the best platform for your team collaboration needs.',
    tool1: {
      name: 'Slack',
      description: 'Professional business communication platform',
      pros: ['Enterprise features', 'Professional interface', 'Advanced integrations', 'Compliance tools'],
      cons: ['Expensive for large teams', 'Complex pricing', 'Limited free tier', 'Can be overwhelming'],
      pricing: 'Free / $7.25-15/month per user',
      rating: 4.3
    },
    tool2: {
      name: 'Discord',
      description: 'Gaming-focused communication with business potential',
      pros: ['Free for most features', 'Excellent voice quality', 'Community building', 'Easy to use'],
      cons: ['Gaming-focused branding', 'Limited business features', 'Less professional', 'Security concerns'],
      pricing: 'Free / $5-10/month',
      rating: 4.1
    },
    relatedKeywords: ['team communication', 'business chat', 'remote work'],
    searchKeywords: ['slack vs discord business', 'team communication tools', 'business chat apps']
  },
  'zoom-vs-google-meet': {
    title: 'Zoom vs Google Meet: Video Conferencing Comparison',
    description: 'Compare Zoom and Google Meet for video conferencing and remote meetings. Choose the best platform for your business needs.',
    tool1: {
      name: 'Zoom',
      description: 'Leading video conferencing platform',
      pros: ['Superior video quality', 'Advanced features', 'Reliable performance', 'Recording capabilities'],
      cons: ['Security concerns', 'Expensive plans', 'Complex interface', 'Resource intensive'],
      pricing: 'Free / $14.99-19.99/month',
      rating: 4.4
    },
    tool2: {
      name: 'Google Meet',
      description: 'Google\'s video conferencing solution',
      pros: ['Google Workspace integration', 'Simple interface', 'Good security', 'Affordable pricing'],
      cons: ['Limited features', 'Requires Google account', 'Less customization', 'Basic recording'],
      pricing: 'Free / $6-18/month',
      rating: 4.2
    },
    relatedKeywords: ['video conferencing', 'remote meetings', 'business communication'],
    searchKeywords: ['zoom vs google meet', 'video conferencing tools', 'remote meeting software']
  },
  'shopify-vs-woocommerce': {
    title: 'Shopify vs WooCommerce: E-commerce Platform Comparison',
    description: 'Compare Shopify and WooCommerce for building online stores. Find the best e-commerce solution for your business.',
    tool1: {
      name: 'Shopify',
      description: 'Hosted e-commerce platform',
      pros: ['Easy setup', 'Hosted solution', 'App ecosystem', '24/7 support'],
      cons: ['Monthly fees', 'Transaction fees', 'Limited customization', 'Vendor lock-in'],
      pricing: '$29-299/month + fees',
      rating: 4.5
    },
    tool2: {
      name: 'WooCommerce',
      description: 'WordPress-based e-commerce plugin',
      pros: ['Open source', 'Full customization', 'No transaction fees', 'WordPress integration'],
      cons: ['Requires hosting', 'Technical knowledge needed', 'Maintenance required', 'Security responsibility'],
      pricing: 'Free + hosting costs',
      rating: 4.3
    },
    relatedKeywords: ['e-commerce', 'online store', 'wordpress'],
    searchKeywords: ['shopify vs woocommerce', 'ecommerce platform comparison', 'online store builder']
  },
  'mailchimp-vs-convertkit': {
    title: 'Mailchimp vs ConvertKit: Email Marketing Comparison',
    description: 'Compare Mailchimp and ConvertKit for email marketing campaigns. Choose the best platform for your email strategy.',
    tool1: {
      name: 'Mailchimp',
      description: 'Popular email marketing platform',
      pros: ['User-friendly interface', 'Free tier available', 'Advanced analytics', 'Template library'],
      cons: ['Expensive as you scale', 'Limited automation', 'Complex pricing', 'Deliverability issues'],
      pricing: 'Free / $10-300/month',
      rating: 4.2
    },
    tool2: {
      name: 'ConvertKit',
      description: 'Creator-focused email marketing tool',
      pros: ['Creator-focused features', 'Advanced automation', 'Better deliverability', 'Simple pricing'],
      cons: ['Higher starting price', 'Limited templates', 'Fewer integrations', 'Learning curve'],
      pricing: '$29-79/month',
      rating: 4.6
    },
    relatedKeywords: ['email marketing', 'newsletter', 'creator tools'],
    searchKeywords: ['mailchimp vs convertkit', 'email marketing tools', 'newsletter platforms']
  },
  'canva-vs-figma': {
    title: 'Canva vs Figma: Design Tool Comparison',
    description: 'Compare Canva and Figma for design and creative work. Find the best tool for your design needs and skill level.',
    tool1: {
      name: 'Canva',
      description: 'User-friendly graphic design platform',
      pros: ['Easy to use', 'Template library', 'No design skills needed', 'Affordable pricing'],
      cons: ['Limited customization', 'Generic designs', 'Subscription model', 'Less professional'],
      pricing: 'Free / $12.99-30/month',
      rating: 4.4
    },
    tool2: {
      name: 'Figma',
      description: 'Professional design and prototyping tool',
      pros: ['Professional features', 'Real-time collaboration', 'Vector-based', 'Free for individuals'],
      cons: ['Steep learning curve', 'Requires design knowledge', 'Complex interface', 'Limited templates'],
      pricing: 'Free / $12-45/month per user',
      rating: 4.7
    },
    relatedKeywords: ['design tools', 'graphic design', 'prototyping'],
    searchKeywords: ['canva vs figma', 'design software comparison', 'graphic design tools']
  },
  'airtable-vs-notion-database': {
    title: 'Airtable vs Notion Database: Data Management Comparison',
    description: 'Compare Airtable and Notion for database and data management needs. Find the best solution for organizing your information.',
    tool1: {
      name: 'Airtable',
      description: 'Spreadsheet-database hybrid platform',
      pros: ['Powerful database features', 'Excellent API', 'Advanced filtering', 'Professional templates'],
      cons: ['Expensive for teams', 'Complex pricing', 'Limited formatting', 'Steep learning curve'],
      pricing: 'Free / $20-45/month per user',
      rating: 4.5
    },
    tool2: {
      name: 'Notion Database',
      description: 'All-in-one workspace with database features',
      pros: ['Integrated with notes', 'Flexible layouts', 'Rich content types', 'Better value'],
      cons: ['Slower performance', 'Limited database features', 'Less powerful API', 'Can be overwhelming'],
      pricing: 'Free / $8-16/month per user',
      rating: 4.3
    },
    relatedKeywords: ['database', 'data management', 'productivity'],
    searchKeywords: ['airtable vs notion', 'database tools comparison', 'data management software']
  },
  'hubspot-vs-pipedrive': {
    title: 'HubSpot vs Pipedrive: CRM Software Comparison',
    description: 'Compare HubSpot and Pipedrive for customer relationship management. Choose the best CRM for your sales team.',
    tool1: {
      name: 'HubSpot',
      description: 'Comprehensive inbound marketing and CRM platform',
      pros: ['All-in-one solution', 'Free tier available', 'Advanced automation', 'Excellent support'],
      cons: ['Expensive premium features', 'Complex setup', 'Overwhelming interface', 'Steep learning curve'],
      pricing: 'Free / $45-1200/month',
      rating: 4.4
    },
    tool2: {
      name: 'Pipedrive',
      description: 'Sales-focused CRM platform',
      pros: ['Sales-focused design', 'Easy to use', 'Visual pipeline', 'Affordable pricing'],
      cons: ['Limited marketing features', 'Basic automation', 'Fewer integrations', 'Limited customization'],
      pricing: '$14.90-99/month per user',
      rating: 4.2
    },
    relatedKeywords: ['CRM', 'sales management', 'customer relationship'],
    searchKeywords: ['hubspot vs pipedrive', 'crm software comparison', 'sales management tools']
  },
  'asana-vs-monday': {
    title: 'Asana vs Monday.com: Project Management Comparison',
    description: 'Compare Asana and Monday.com for project management and team collaboration. Find the best tool for your workflow.',
    tool1: {
      name: 'Asana',
      description: 'Flexible project management platform',
      pros: ['Flexible project views', 'Strong free tier', 'Good mobile app', 'Timeline features'],
      cons: ['Limited reporting', 'Complex for simple tasks', 'Steep learning curve', 'Expensive premium'],
      pricing: 'Free / $10.99-24.99/month per user',
      rating: 4.3
    },
    tool2: {
      name: 'Monday.com',
      description: 'Visual project management platform',
      pros: ['Visual interface', 'Customizable workflows', 'Good automation', 'Excellent support'],
      cons: ['No free tier', 'Expensive pricing', 'Can be overwhelming', 'Limited free features'],
      pricing: '$8-16/month per user',
      rating: 4.4
    },
    relatedKeywords: ['project management', 'team collaboration', 'workflow'],
    searchKeywords: ['asana vs monday', 'project management tools', 'team collaboration software']
  },
  'stripe-vs-paypal': {
    title: 'Stripe vs PayPal: Payment Processing Comparison',
    description: 'Compare Stripe and PayPal for online payment processing. Choose the best payment solution for your business.',
    tool1: {
      name: 'Stripe',
      description: 'Developer-friendly payment processing',
      pros: ['Developer-friendly API', 'Global coverage', 'Advanced features', 'Transparent pricing'],
      cons: ['Technical setup required', 'Limited customer support', 'Holds on new accounts', 'Complex documentation'],
      pricing: '2.9% + 30¢ per transaction',
      rating: 4.5
    },
    tool2: {
      name: 'PayPal',
      description: 'Popular online payment platform',
      pros: ['Easy setup', 'Brand recognition', 'Buyer protection', 'Multiple payment options'],
      cons: ['Higher fees', 'Account holds', 'Limited customization', 'Poor customer service'],
      pricing: '2.9-3.5% + fixed fee',
      rating: 3.9
    },
    relatedKeywords: ['payment processing', 'online payments', 'e-commerce'],
    searchKeywords: ['stripe vs paypal', 'payment processor comparison', 'online payment solutions']
  },

  'reddit-vs-twitter-startup-research': {
    title: 'Reddit vs Twitter for Startup Market Research',
    description: 'Compare Reddit and Twitter for discovering startup opportunities and market validation. Find the best platform for your research needs.',
    tool1: {
      name: 'Reddit',
      description: 'Community-driven discussion platform',
      pros: ['Deep, detailed discussions', 'Niche communities', 'Honest feedback', 'Long-form content'],
      cons: ['Slower trend discovery', 'Complex navigation', 'Varied quality', 'Time-intensive research'],
      pricing: 'Free',
      rating: 4.6
    },
    tool2: {
      name: 'Twitter',
      description: 'Real-time social media platform',
      pros: ['Real-time trends', 'Quick insights', 'Influencer access', 'Easy to monitor'],
      cons: ['Surface-level discussions', 'Information overload', 'Short content format', 'Algorithm bias'],
      pricing: 'Free / $8/month',
      rating: 4.1
    },
    relatedKeywords: ['market research', 'startup validation', 'social listening', 'trend analysis'],
    searchKeywords: ['reddit vs twitter startup research', 'social media market research', 'startup idea discovery platforms', 'market validation tools']
  },
  'ai-vs-traditional-market-research': {
    title: 'AI-Powered vs Traditional Market Research for Startups',
    description: 'Compare AI tools with traditional market research methods for startup validation. Discover which approach delivers better insights.',
    tool1: {
      name: 'AI-Powered Research',
      description: 'Automated analysis using artificial intelligence',
      pros: ['Fast data processing', 'Large-scale analysis', 'Pattern recognition', 'Cost-effective', '24/7 availability'],
      cons: ['Limited context understanding', 'Potential bias', 'Requires data quality', 'Less human insight'],
      pricing: '$50-500/month',
      rating: 4.3
    },
    tool2: {
      name: 'Traditional Research',
      description: 'Human-led surveys, interviews, and focus groups',
      pros: ['Deep human insights', 'Contextual understanding', 'Flexible questioning', 'Proven methods', 'Qualitative depth'],
      cons: ['Time-intensive', 'Expensive', 'Small sample sizes', 'Human bias', 'Slow turnaround'],
      pricing: '$5,000-50,000/project',
      rating: 4.0
    },
    relatedKeywords: ['market research', 'startup validation', 'AI analysis', 'business intelligence'],
    searchKeywords: ['ai market research tools', 'traditional vs ai market research', 'startup validation methods', 'automated market analysis']
  },
  'free-vs-paid-startup-validation-tools': {
    title: 'Free vs Paid Startup Validation Tools Comparison',
    description: 'Compare free and paid tools for validating your startup idea and market research. Find the best validation approach for your budget.',
    tool1: {
      name: 'Free Validation Tools',
      description: 'No-cost tools for startup idea validation',
      pros: ['Zero upfront cost', 'Good for beginners', 'Basic functionality', 'Risk-free testing', 'Community support'],
      cons: ['Limited features', 'Basic analytics', 'No premium support', 'Data limitations', 'Time-consuming'],
      pricing: 'Free',
      rating: 3.8
    },
    tool2: {
      name: 'Paid Validation Tools',
      description: 'Premium tools with advanced validation features',
      pros: ['Advanced analytics', 'Professional support', 'Comprehensive data', 'Time-saving', 'Better accuracy'],
      cons: ['Monthly costs', 'Learning curve', 'Overkill for simple validation', 'Subscription commitment'],
      pricing: '$29-299/month',
      rating: 4.5
    },
    relatedKeywords: ['startup validation', 'market research tools', 'business validation', 'idea testing'],
    searchKeywords: ['free startup validation tools', 'paid vs free market research', 'startup validation budget', 'business idea testing tools']
  },
  'supabase-vs-firebase-startup-backend': {
    title: 'Supabase vs Firebase for Startup Backend',
    description: 'Compare Supabase and Firebase for startup backend development. Find the best database and backend solution for your startup.',
    tool1: {
      name: 'Supabase',
      description: 'Open-source Firebase alternative',
      pros: ['Open source', 'PostgreSQL database', 'Real-time subscriptions', 'Built-in auth', 'SQL support'],
      cons: ['Newer platform', 'Smaller ecosystem', 'Limited integrations', 'Learning curve for SQL'],
      pricing: 'Free / $25-2,490/month',
      rating: 4.4
    },
    tool2: {
      name: 'Firebase',
      description: 'Google\'s mobile and web development platform',
      pros: ['Google ecosystem', 'Mature platform', 'Extensive documentation', 'Large community', 'Easy scaling'],
      cons: ['Vendor lock-in', 'NoSQL limitations', 'Complex pricing', 'Google dependency'],
      pricing: 'Free / Pay-as-you-go',
      rating: 4.3
    },
    relatedKeywords: ['backend development', 'database', 'startup tech stack', 'web development'],
    searchKeywords: ['supabase vs firebase', 'startup backend comparison', 'database for startups', 'backend as a service']
  },
  'vercel-vs-netlify-startup-deployment': {
    title: 'Vercel vs Netlify for Startup Deployment',
    description: 'Compare Vercel and Netlify for deploying your startup\'s web applications. Find the best hosting platform for your needs.',
    tool1: {
      name: 'Vercel',
      description: 'Frontend deployment and hosting platform',
      pros: ['Next.js optimization', 'Edge functions', 'Global CDN', 'Zero-config deployment', 'Excellent performance'],
      cons: ['Focused on React/Next.js', 'Function limitations', 'Pricing can scale up', 'Less flexibility'],
      pricing: 'Free / $20-400/month',
      rating: 4.6
    },
    tool2: {
      name: 'Netlify',
      description: 'All-in-one platform for modern web projects',
      pros: ['Framework agnostic', 'Built-in forms', 'Split testing', 'Branch deploys', 'Generous free tier'],
      cons: ['Build time limits', 'Function cold starts', 'Complex pricing tiers', 'Performance variations'],
      pricing: 'Free / $19-99/month',
      rating: 4.4
    },
    relatedKeywords: ['web hosting', 'deployment', 'frontend hosting', 'startup infrastructure'],
    searchKeywords: ['vercel vs netlify', 'startup deployment platform', 'frontend hosting comparison', 'web app deployment']
  }
};

export default function Alternatives() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();

  const comparisonSlug = params.comparison;
  const config = ALTERNATIVE_CONFIGS[comparisonSlug as keyof typeof ALTERNATIVE_CONFIGS];

  // Get related ideas based on keywords
  const { data: ideasData, isLoading: ideasLoading } = useIdeas({
    sortBy: 'upvotes',
    pageSize: 20
  });

  // Filter ideas by related keywords
  const relatedIdeas = ideasData?.ideas?.filter(idea => 
    config?.relatedKeywords?.some(keyword => 
      idea.keywords?.some(k => k.toLowerCase().includes(keyword.toLowerCase())) ||
      idea.title.toLowerCase().includes(keyword.toLowerCase()) ||
      idea.summary?.toLowerCase().includes(keyword.toLowerCase())
    )
  ) || [];

  // Handle 404 if comparison not found
  useEffect(() => {
    if (comparisonSlug && !config) {
      setLocation('/404');
    }
  }, [comparisonSlug, config, setLocation]);

  if (!config) {
    return (
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto space-y-8">
            <Skeleton className="h-12 w-3/4 bg-white/10" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Skeleton className="h-64 bg-white/10" />
              <Skeleton className="h-64 bg-white/10" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHead
        title={`${config.title} | IdeaHunter`}
        description={config.description}
        keywords={[
          ...config.searchKeywords,
          'startup tools comparison',
          'business software alternatives',
          'productivity tools 2025',
          ...config.relatedKeywords
        ]}
        type="website"
      />

      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative min-h-screen">
        <ParticleBackground />

        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">

            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <Button
                onClick={() => setLocation('/')}
                variant="ghost"
                className="mb-6 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>

              <div className="text-center mb-8">
                <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                  {config.title}
                </h1>
                <p className="text-xl text-gray-300 max-w-4xl mx-auto">
                  {config.description}
                </p>
              </div>
            </motion.div>

            {/* Comparison Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"
            >
              {/* Tool 1 */}
              <Card className="glass-card border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center justify-between">
                    <span className="flex items-center">
                      <Zap className="w-6 h-6 mr-2 text-blue-400" />
                      {config.tool1.name}
                    </span>
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 mr-1" />
                      <span className="text-yellow-400">{config.tool1.rating}</span>
                    </div>
                  </CardTitle>
                  <p className="text-gray-400">{config.tool1.description}</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h4 className="text-green-400 font-semibold mb-2 flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Pros
                    </h4>
                    <ul className="space-y-1">
                      {config.tool1.pros.map((pro, index) => (
                        <li key={index} className="text-gray-300 text-sm flex items-start">
                          <span className="text-green-400 mr-2">•</span>
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="text-red-400 font-semibold mb-2 flex items-center">
                      <XCircle className="w-4 h-4 mr-2" />
                      Cons
                    </h4>
                    <ul className="space-y-1">
                      {config.tool1.cons.map((con, index) => (
                        <li key={index} className="text-gray-300 text-sm flex items-start">
                          <span className="text-red-400 mr-2">•</span>
                          {con}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="pt-4 border-t border-white/10">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Pricing:</span>
                      <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                        {config.tool1.pricing}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Tool 2 */}
              <Card className="glass-card border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center justify-between">
                    <span className="flex items-center">
                      <Zap className="w-6 h-6 mr-2 text-purple-400" />
                      {config.tool2.name}
                    </span>
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 mr-1" />
                      <span className="text-yellow-400">{config.tool2.rating}</span>
                    </div>
                  </CardTitle>
                  <p className="text-gray-400">{config.tool2.description}</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h4 className="text-green-400 font-semibold mb-2 flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Pros
                    </h4>
                    <ul className="space-y-1">
                      {config.tool2.pros.map((pro, index) => (
                        <li key={index} className="text-gray-300 text-sm flex items-start">
                          <span className="text-green-400 mr-2">•</span>
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="text-red-400 font-semibold mb-2 flex items-center">
                      <XCircle className="w-4 h-4 mr-2" />
                      Cons
                    </h4>
                    <ul className="space-y-1">
                      {config.tool2.cons.map((con, index) => (
                        <li key={index} className="text-gray-300 text-sm flex items-start">
                          <span className="text-red-400 mr-2">•</span>
                          {con}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="pt-4 border-t border-white/10">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Pricing:</span>
                      <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                        {config.tool2.pricing}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Related Ideas */}
            {relatedIdeas.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mb-8"
              >
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-white flex items-center">
                    <AlertCircle className="w-6 h-6 mr-2 text-orange-400" />
                    Related Startup Opportunities
                  </h2>
                  {!user && (
                    <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                      Sign in to see all ideas
                    </Badge>
                  )}
                </div>

                <IdeaGrid
                  ideas={relatedIdeas.slice(0, 6)}
                  isLoading={ideasLoading}
                  isLimited={!user}
                  useNavigation={true}
                  isFetching={ideasLoading}
                />
              </motion.div>
            )}

            {/* Related blog articles */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                <BookOpen className="w-6 h-6 mr-2 text-blue-400" />
                深度阅读
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="glass-card border-white/10 hover:border-white/20 transition-all duration-200">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">
                      <a href="/blog/complete-guide-finding-startup-ideas-reddit-2025" className="hover:text-blue-400 transition-colors">
                        Complete Guide to Finding Startup Ideas on Reddit
                      </a>
                    </h3>
                    <p className="text-gray-400 text-sm mb-3">
                      Learn the methodology behind discovering profitable startup opportunities through social media analysis.
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <span>8 min read</span>
                      <span className="mx-2">•</span>
                      <span>Tutorial Guide</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-white/10 hover:border-white/20 transition-all duration-200">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">
                      <a href="/blog/reddit-vs-traditional-market-research-startup-ideas" className="hover:text-blue-400 transition-colors">
                        Reddit vs Traditional Market Research
                      </a>
                    </h3>
                    <p className="text-gray-400 text-sm mb-3">
                      Compare different approaches to market research and discover why Reddit often reveals better opportunities.
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <span>6 min read</span>
                      <span className="mx-2">•</span>
                      <span>Data Analysis</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>

          </div>
        </div>
      </div>
    </>
  );
}
