import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, Trophy, TrendingUp, Star, Calendar, Target, Crown, Medal, Award, BookOpen } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth.tsx";
import { useIdeas } from "@/hooks/use-ideas";
import { useIndustries } from "@/hooks/use-industries";
import { usePremiumAccess } from "@/hooks/use-subscription";
import SEOHead from "@/components/seo-head";
import ParticleBackground from "@/components/particle-background";
import IdeaGrid from "@/components/idea-grid";
import LockedIdeaCard from "@/components/locked-idea-card";
import AuthModal from "@/components/auth-modal";

import type { Industry, StartupIdea } from "@/lib/types";

// Category configurations
const CATEGORY_CONFIGS = {
  'ai-startup-ideas-2025': {
    title: 'Best AI Startup Ideas 2025',
    description: 'Top AI and machine learning startup opportunities discovered from Reddit communities. These ideas represent the most promising AI business ventures with high market potential.',
    industryFilter: 'AI & Machine Learning',
    keywords: ['AI startup ideas', 'machine learning business', 'AI opportunities 2025', 'artificial intelligence startup'],
    icon: '🤖'
  },
  'saas-business-opportunities': {
    title: 'Best SaaS Business Opportunities',
    description: 'Top Software-as-a-Service startup ideas with proven market demand. Discover cloud-based business opportunities with recurring revenue potential.',
    industryFilter: 'SaaS & Cloud Services',
    keywords: ['SaaS startup ideas', 'cloud business opportunities', 'software startup', 'SaaS business model'],
    icon: '☁️'
  },
  'startup-ideas-2025': {
    title: 'Best Startup Ideas 2025',
    description: 'The ultimate ranking of the most promising startup opportunities for 2025. Curated from thousands of Reddit discussions and validated by AI analysis.',
    industryFilter: null, // All industries
    keywords: ['startup ideas 2025', 'best business ideas', 'startup opportunities', 'business ideas 2025'],
    icon: '🚀'
  },
  'profitable-business-ideas': {
    title: 'Most Profitable Business Ideas',
    description: 'High-profit potential business ideas with proven market demand and clear monetization strategies. Focus on ideas with strong revenue potential.',
    industryFilter: null,
    keywords: ['profitable business ideas', 'high profit startup', 'money making ideas', 'profitable startup'],
    icon: '💰'
  },
  // Second Priority Pages - Based on Data Volume
  'financial-independence-ideas': {
    title: 'Best Financial Independence Startup Ideas',
    description: 'Top startup opportunities in personal finance and financial independence. 72+ validated ideas for building wealth and financial freedom.',
    industryFilter: 'Financial Independence & Personal Finance',
    keywords: ['financial independence startup', 'fintech ideas', 'personal finance business', 'wealth building startup'],
    icon: '💸'
  },
  'family-parenting-business-ideas': {
    title: 'Best Family & Parenting Business Ideas',
    description: 'Innovative startup opportunities in family and parenting space. 50+ ideas addressing real parent pain points and family needs.',
    industryFilter: 'Family & Parenting',
    keywords: ['parenting startup ideas', 'family business opportunities', 'parent tech startup', 'childcare business'],
    icon: '👨‍👩‍👧‍👦'
  },
  'pet-care-startup-opportunities': {
    title: 'Best Pet Care Startup Opportunities',
    description: 'Promising business ideas in the growing pet care industry. 48+ opportunities in pet tech, services, and products.',
    industryFilter: 'Pet Care & Community',
    keywords: ['pet startup ideas', 'pet care business', 'pet tech opportunities', 'animal care startup'],
    icon: '🐕'
  },
  'startup-business-ideas': {
    title: 'Best Startup & Business Ideas',
    description: 'General startup and business opportunities across all sectors. 42+ diverse ideas for aspiring entrepreneurs.',
    industryFilter: 'Startup & Business',
    keywords: ['startup business ideas', 'general business opportunities', 'entrepreneur ideas', 'business startup'],
    icon: '💼'
  },
  'edtech-startup-ideas': {
    title: 'Best EdTech Startup Ideas',
    description: 'Educational technology startup opportunities transforming learning. 28+ ideas in online education and learning platforms.',
    industryFilter: 'EdTech',
    keywords: ['edtech startup ideas', 'education technology', 'online learning business', 'educational startup'],
    icon: '📚'
  },
  'health-fitness-tech-ideas': {
    title: 'Best Health & Fitness Tech Ideas',
    description: 'Health and fitness technology startup opportunities. 29+ ideas in digital health, fitness apps, and wellness tech.',
    industryFilter: 'Health & Fitness Tech',
    keywords: ['health tech startup', 'fitness app ideas', 'wellness technology', 'digital health business'],
    icon: '💪'
  },
  // High Search Volume Keywords
  'online-business-opportunities': {
    title: 'Best Online Business Opportunities',
    description: 'Top online business ideas and digital startup opportunities. Discover internet-based businesses with global reach potential.',
    industryFilter: null,
    keywords: ['online business opportunities', 'digital business ideas', 'internet startup', 'online business model'],
    icon: '🌐'
  },
  // Phase 1 SEO Expansion - Week 3-4: Budget-Oriented Pages
  'startup-ideas-under-1000-budget': {
    title: 'Best Startup Ideas Under $1,000 Budget',
    description: 'Discover profitable startup opportunities you can launch with less than $1,000. Low-cost business ideas with high potential returns.',
    industryFilter: null,
    keywords: ['startup ideas under 1000 dollars', 'low budget startup opportunities', 'cheap business ideas', 'bootstrap startup ideas'],
    icon: '💵'
  },
  'startup-ideas-under-5000-budget': {
    title: 'Best Startup Ideas Under $5,000 Budget',
    description: 'Mid-budget startup opportunities with proven market demand. Business ideas you can start with $5,000 or less.',
    industryFilter: null,
    keywords: ['startup ideas under 5000 budget', 'mid budget business ideas', '5k startup opportunities', 'affordable business ventures'],
    icon: '💰'
  },
  'startup-ideas-under-10000-budget': {
    title: 'Best Startup Ideas Under $10,000 Budget',
    description: 'Higher-budget startup opportunities with strong growth potential. Scalable business ideas for serious entrepreneurs.',
    industryFilter: null,
    keywords: ['startup ideas under 10000 budget', 'high budget startup opportunities', '10k business ideas', 'investment startup ideas'],
    icon: '💎'
  },
  'no-code-startup-ideas-2025': {
    title: 'Best No-Code Startup Ideas 2025',
    description: 'Build successful startups without programming skills. No-code business opportunities using modern tools and platforms.',
    industryFilter: null,
    keywords: ['no code startup ideas', 'no programming business ideas', 'non technical startup opportunities', 'no code business 2025'],
    icon: '🛠️'
  },
  'one-person-startup-ideas': {
    title: 'Best One-Person Startup Ideas',
    description: 'Solo entrepreneur opportunities perfect for individual founders. Build and scale businesses as a single person.',
    industryFilter: null,
    keywords: ['one person startup ideas', 'solo entrepreneur business', 'single founder startup', 'solopreneur opportunities'],
    icon: '👤'
  },
  // Skill-Oriented Pages
  'startup-ideas-for-developers': {
    title: 'Best Startup Ideas for Software Developers',
    description: 'Technical startup opportunities perfect for developers and programmers. Leverage your coding skills to build profitable businesses.',
    industryFilter: null,
    keywords: ['startup ideas for developers', 'technical business opportunities', 'programmer startup ideas', 'developer entrepreneur ideas'],
    icon: '💻'
  },
  'startup-ideas-for-designers': {
    title: 'Best Startup Ideas for Designers',
    description: 'Creative business opportunities for designers and visual artists. Turn your design skills into profitable ventures.',
    industryFilter: null,
    keywords: ['startup ideas for designers', 'design business opportunities', 'creative entrepreneur ideas', 'designer startup ventures'],
    icon: '🎨'
  },
  'startup-ideas-for-marketers': {
    title: 'Best Startup Ideas for Marketers',
    description: 'Marketing-focused business opportunities for digital marketers and growth experts. Monetize your marketing expertise.',
    industryFilter: null,
    keywords: ['startup ideas for marketers', 'marketing business opportunities', 'digital marketing startup', 'growth hacker business ideas'],
    icon: '📈'
  },
  'startup-ideas-no-technical-skills': {
    title: 'Best Startup Ideas for Non-Technical Founders',
    description: 'Business opportunities that don\'t require technical skills. Perfect for non-technical entrepreneurs and business-minded individuals.',
    industryFilter: null,
    keywords: ['non technical startup ideas', 'business ideas no coding', 'startup opportunities no programming', 'non tech entrepreneur ideas'],
    icon: '🤝'
  }
};

export default function BestIdeas() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();
  const { canAccessPremiumFeatures } = usePremiumAccess();
  const [authModalOpen, setAuthModalOpen] = useState(false);

  const category = params.category;
  const config = CATEGORY_CONFIGS[category as keyof typeof CATEGORY_CONFIGS];

  // Get all industries to find the one matching the filter
  const { data: industries, isLoading: industriesLoading } = useIndustries();
  const targetIndustry = config?.industryFilter
    ? industries?.find(ind => ind.name === config.industryFilter)
    : null;

  // Get ideas based on category - only when we have the target industry or no filter needed
  const { data: ideasData, isLoading: ideasLoading } = useIdeas({
    industryId: targetIndustry?.id,
    sortBy: 'upvotes',
    pageSize: 50
  });

  // Get category stats - only when we have the target industry resolved or no filter needed
  const { data: categoryStats } = useQuery({
    queryKey: ['category-stats', category, targetIndustry?.id],
    queryFn: async () => {
      const { supabase } = await import('@/lib/queryClient');

      let query = supabase.from('startup_ideas').select('*', { count: 'exact', head: true });

      if (targetIndustry?.id) {
        query = query.eq('industry_id', targetIndustry.id);
      }

      const { count: totalIdeas } = await query;

      // Get average upvotes for this category
      let upvotesQuery = supabase.from('startup_ideas').select('upvotes');
      if (targetIndustry?.id) {
        upvotesQuery = upvotesQuery.eq('industry_id', targetIndustry.id);
      }

      const { data: upvotesData } = await upvotesQuery;
      const avgUpvotes = upvotesData && upvotesData.length > 0
        ? Math.round(upvotesData.reduce((sum, item) => sum + (item.upvotes || 0), 0) / upvotesData.length)
        : 0;

      // Get top performing idea
      let topIdeaQuery = supabase
        .from('startup_ideas')
        .select('title, upvotes')
        .order('upvotes', { ascending: false })
        .limit(1);

      if (targetIndustry?.id) {
        topIdeaQuery = topIdeaQuery.eq('industry_id', targetIndustry.id);
      }

      const { data: topIdea } = await topIdeaQuery;

      return {
        totalIdeas: totalIdeas || 0,
        avgUpvotes,
        topIdea: topIdea?.[0] || null
      };
    },
    enabled: !!config && (!config.industryFilter || (!!targetIndustry && !industriesLoading))
  });

  // Handle 404 if category not found
  useEffect(() => {
    if (!config) {
      setLocation('/404');
    }
  }, [config, setLocation]);

  if (!config) {
    return null;
  }

  // Show loading if industries are loading (for filtered categories) or ideas are loading
  if (industriesLoading || ideasLoading || (config.industryFilter && !targetIndustry)) {
    return (
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto space-y-8">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-10 rounded-lg bg-white/10" />
              <Skeleton className="h-6 w-20 bg-white/10" />
            </div>
            <Skeleton className="h-12 w-3/4 bg-white/10" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Skeleton className="h-32 bg-white/10" />
              <Skeleton className="h-32 bg-white/10" />
              <Skeleton className="h-32 bg-white/10" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  const getRankIcon = (index: number) => {
    if (index === 0) return <Crown className="w-6 h-6 text-yellow-400" />;
    if (index === 1) return <Medal className="w-6 h-6 text-gray-300" />;
    if (index === 2) return <Award className="w-6 h-6 text-amber-600" />;
    return <span className="w-6 h-6 flex items-center justify-center text-lg font-bold text-gray-400">#{index + 1}</span>;
  };

  return (
    <>
      <SEOHead 
        title={`${config.title} | IdeaHunter`}
        description={config.description}
        keywords={config.keywords}
        type="website"
      />
      
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <Button
                onClick={() => setLocation('/')}
                variant="ghost"
                className="mb-6 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>

              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 rounded-xl bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center">
                  <Trophy className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                    {config.title}
                  </h1>
                  <p className="text-xl text-gray-300">
                    Ranked by Community Validation & Market Potential
                  </p>
                </div>
              </div>

              <p className="text-lg text-gray-300 leading-relaxed max-w-4xl">
                {config.description}
              </p>
            </motion.div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Trophy className="w-8 h-8 text-yellow-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {categoryStats?.totalIdeas || 0}
                      </p>
                      <p className="text-gray-400">Total Ideas</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <TrendingUp className="w-8 h-8 text-green-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {categoryStats?.avgUpvotes || 0}
                      </p>
                      <p className="text-gray-400">Avg Upvotes</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Star className="w-8 h-8 text-purple-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {categoryStats?.topIdea?.upvotes || 0}
                      </p>
                      <p className="text-gray-400">Top Idea Score</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Winner Spotlight */}
            {categoryStats?.topIdea && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="mb-8"
              >
                <Card className="glass-card border-yellow-400/30 bg-gradient-to-r from-yellow-400/10 to-orange-500/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Crown className="w-6 h-6 mr-2 text-yellow-400" />
                      #1 Most Validated Idea
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2">
                          {categoryStats.topIdea.title}
                        </h3>
                        <div className="flex items-center space-x-4">
                          <Badge className="bg-yellow-400/20 text-yellow-400 border-yellow-400/30">
                            {categoryStats.topIdea.upvotes} upvotes
                          </Badge>
                          <Badge className="bg-green-400/20 text-green-400 border-green-400/30">
                            Community Validated
                          </Badge>
                        </div>
                      </div>
                      <Trophy className="w-12 h-12 text-yellow-400" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Ideas Ranking */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white flex items-center">
                  <Trophy className="w-6 h-6 mr-2 text-yellow-400" />
                  Top Ranked Ideas
                </h2>
                {!user && (
                  <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                    Sign in to see all ideas
                  </Badge>
                )}
              </div>

              {/* Custom ranked grid */}
              <div className="space-y-4">
                {(ideasData?.ideas || []).slice(0, 10).map((idea, index) => (
                  <motion.div
                    key={idea.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                    className="glass-card border-white/10 p-6 hover:border-white/20 transition-all duration-200 cursor-pointer"
                    onClick={() => {
                      // Check if user is logged in before allowing navigation to idea details
                      if (!user) {
                        setAuthModalOpen(true);
                        return;
                      }
                      setLocation(`/idea/${idea.id}/${idea.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}`);
                    }}
                  >
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        {getRankIcon(index)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                              {idea.title}
                            </h3>
                            <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                              {idea.summary}
                            </p>
                            <div className="flex items-center space-x-4 text-sm">
                              <div className="flex items-center space-x-1">
                                <TrendingUp className="w-4 h-4 text-green-400" />
                                <span className="text-green-400">{idea.upvotes} upvotes</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Target className="w-4 h-4 text-blue-400" />
                                <span className="text-blue-400">{idea.industry?.name}</span>
                              </div>
                              {idea.keywords && idea.keywords.length > 0 && (
                                <Badge className="bg-purple-400/20 text-purple-400 text-xs">
                                  {idea.keywords[0]}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="flex-shrink-0 ml-4">
                            <Badge 
                              className={`${
                                index < 3 
                                  ? 'bg-yellow-400/20 text-yellow-400 border-yellow-400/30' 
                                  : 'bg-gray-600/20 text-gray-400 border-gray-600/30'
                              }`}
                            >
                              Rank #{index + 1}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>



              {/* Show more ideas in regular grid - with premium restrictions */}
              {(ideasData?.ideas || []).length > 10 && (
                <div className="mt-8">
                  <h3 className="text-xl font-bold text-white mb-6">More Great Ideas</h3>

                  {/* For free users, limit to top 5 additional ideas (after top 10) */}
                  {user && !canAccessPremiumFeatures ? (
                    <div className="space-y-6">
                      {/* Show top 5 additional ideas for free users */}
                      <IdeaGrid
                        ideas={(ideasData?.ideas || []).slice(10, 15)}
                        isLoading={false}
                        isLimited={false}
                        useNavigation={true}
                        isFetching={false}
                      />

                      {/* Show locked cards for remaining ideas */}
                      {(ideasData?.ideas || []).length > 15 && (
                        <div className="space-y-4">
                          <div className="text-center py-4">
                            <h3 className="text-lg font-semibold text-white mb-2">
                              {(ideasData?.ideas || []).length - 15} More Premium Ideas
                            </h3>
                            <p className="text-gray-400 text-sm">
                              Upgrade to Pro to access all {(ideasData?.ideas || []).length} startup ideas
                            </p>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {(ideasData?.ideas || []).slice(15, 18).map((idea, index) => (
                              <LockedIdeaCard key={idea.id} idea={idea} index={index} />
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    /* Pro users and unauthenticated users see all ideas */
                    <IdeaGrid
                      ideas={(ideasData?.ideas || []).slice(10)}
                      isLoading={false}
                      isLimited={ideasData?.isLimited}
                      useNavigation={true}
                      isFetching={false}
                    />
                  )}
                </div>
              )}
            </motion.div>

            {/* Related tutorials */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                <BookOpen className="w-6 h-6 mr-2 text-blue-400" />
                相关教程
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="glass-card border-white/10 hover:border-white/20 transition-all duration-200">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">
                      <a href="/blog/complete-guide-finding-startup-ideas-reddit-2025" className="hover:text-blue-400 transition-colors">
                        Complete Guide to Finding Startup Ideas
                      </a>
                    </h3>
                    <p className="text-gray-400 text-sm mb-3">
                      Step-by-step methodology for discovering profitable startup opportunities through Reddit analysis.
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <span>8 min read</span>
                      <span className="mx-2">•</span>
                      <span>Tutorial</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-white/10 hover:border-white/20 transition-all duration-200">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">
                      <a href="/blog/reddit-comment-to-million-dollar-business-success-stories" className="hover:text-blue-400 transition-colors">
                        From Reddit Comment to $1M Business
                      </a>
                    </h3>
                    <p className="text-gray-400 text-sm mb-3">
                      Real success stories of entrepreneurs who turned Reddit discussions into profitable businesses.
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <span>7 min read</span>
                      <span className="mx-2">•</span>
                      <span>Success Stories</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-white/10 hover:border-white/20 transition-all duration-200">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-2">
                      <a href="/faq/how-to-validate-startup-ideas" className="hover:text-blue-400 transition-colors">
                        Startup Validation Framework
                      </a>
                    </h3>
                    <p className="text-gray-400 text-sm mb-3">
                      Complete guide to validating your startup ideas before investing time and money.
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <span>12 min read</span>
                      <span className="mx-2">•</span>
                      <span>Validation Guide</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>

          </div>
        </div>
      </div>

      <AuthModal
        open={authModalOpen}
        onOpenChange={setAuthModalOpen}
      />
    </>
  );
}
