import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, HelpCircle, ChevronDown, ChevronUp, Search, Lightbulb, TrendingUp, Users, Target, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import SEOHead from "@/components/seo-head";
import ParticleBackground from "@/components/particle-background";

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  keywords: string[];
}

const FAQ_DATA: FAQItem[] = [
  {
    id: "what-is-ideahunter",
    question: "What is IdeaHunter and how does it work?",
    answer: "IdeaHunter is an AI-powered platform that discovers and analyzes trending startup opportunities from Reddit communities. We use advanced AI to scan popular subreddits, identify promising business ideas, and provide detailed market analysis including upvotes, comments, and validation data.",
    category: "Platform",
    keywords: ["ideahunter", "how it works", "AI analysis", "reddit startup ideas"]
  },
  {
    id: "reddit-ideas-reliable",
    question: "Are Reddit startup ideas reliable for building a business?",
    answer: "Reddit startup ideas can be highly valuable because they represent real problems discussed by real people. Our AI analyzes thousands of discussions to identify genuine pain points and market opportunities. However, like any business idea, proper validation and market research are essential before investing time and money.",
    category: "Validation",
    keywords: ["reddit reliability", "startup validation", "market research", "business ideas"]
  },
  {
    id: "how-to-validate-ideas",
    question: "How do I validate a startup idea from IdeaHunter?",
    answer: "Start by researching the problem deeper: talk to potential customers, analyze competitors, create a simple MVP or landing page to test demand. Look at the Reddit discussions we provide - check comment sentiment, upvote ratios, and user engagement. Our platform provides existing solutions analysis to help you understand the competitive landscape.",
    category: "Validation",
    keywords: ["idea validation", "startup validation", "mvp", "market research", "customer research"]
  },
  {
    id: "ai-analysis-accuracy",
    question: "How accurate is the AI analysis of startup ideas?",
    answer: "Our AI analysis combines multiple data points including Reddit engagement metrics, keyword analysis, market size estimation, and competitive landscape assessment. While highly sophisticated, it should be used as a starting point for your research, not a final decision maker. We recommend combining our insights with your own market research.",
    category: "AI Analysis",
    keywords: ["AI accuracy", "analysis quality", "market research", "data reliability"]
  },
  {
    id: "free-vs-premium",
    question: "What's the difference between free and premium access?",
    answer: "Free users can view today's top 10 trending ideas with basic information. Premium users get access to our complete database of 560+ ideas across 35+ industries, historical data, advanced filtering, favorites, and detailed market analysis including competitor insights and solution gaps.",
    category: "Pricing",
    keywords: ["free vs premium", "pricing", "features", "subscription"]
  },
  {
    id: "idea-sources",
    question: "Which Reddit communities do you monitor for startup ideas?",
    answer: "We monitor 35+ industry-specific subreddits including r/entrepreneur, r/startups, r/SaaS, r/artificial, r/fintech, r/edtech, and many others. Our AI identifies the most promising discussions based on engagement, problem clarity, and market potential across these communities.",
    category: "Data Sources",
    keywords: ["reddit communities", "subreddits", "data sources", "monitoring"]
  },
  {
    id: "update-frequency",
    question: "How often is the data updated?",
    answer: "We update our database daily with new startup ideas and market insights. Our AI continuously monitors Reddit discussions and processes new opportunities as they emerge. Premium users get access to real-time updates and trending analysis.",
    category: "Platform",
    keywords: ["data updates", "frequency", "real-time", "daily updates"]
  },
  {
    id: "industry-coverage",
    question: "What industries and business types are covered?",
    answer: "We cover 35+ industries including AI & Machine Learning, SaaS, FinTech, EdTech, HealthTech, E-commerce, Gaming, and many more. Our platform identifies opportunities across B2B, B2C, and marketplace business models, from simple apps to complex enterprise solutions.",
    category: "Industries",
    keywords: ["industries", "business types", "coverage", "sectors"]
  },
  {
    id: "competitor-analysis",
    question: "Do you provide competitor analysis for startup ideas?",
    answer: "Yes! Our AI analyzes existing solutions mentioned in Reddit discussions and identifies solution gaps in the market. We provide insights into what competitors are doing well and where opportunities exist for improvement or differentiation.",
    category: "Analysis",
    keywords: ["competitor analysis", "existing solutions", "market gaps", "differentiation"]
  },
  {
    id: "success-stories",
    question: "Are there success stories of businesses built from Reddit ideas?",
    answer: "Many successful startups have originated from Reddit discussions or similar community insights. While we can't guarantee success, our platform helps you identify ideas with strong community validation and clear market demand - key indicators of potential success.",
    category: "Success",
    keywords: ["success stories", "reddit startups", "validation", "community demand"]
  }
];

const CATEGORIES = ["All", "Platform", "Validation", "AI Analysis", "Pricing", "Data Sources", "Industries", "Analysis", "Success"];

export default function FAQ() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const topic = params.topic;

  // Filter FAQs based on search and category
  const filteredFAQs = FAQ_DATA.filter(faq => {
    const matchesSearch = searchTerm === "" || 
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === "All" || faq.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // If this is a specific FAQ topic, redirect to the specific page
  if (topic === "how-to-validate-startup-ideas") {
    setLocation('/faq/how-to-validate-startup-ideas');
    return null;
  }

  return (
    <>
      <SEOHead 
        title="Frequently Asked Questions | IdeaHunter"
        description="Get answers to common questions about IdeaHunter, startup idea validation, Reddit business opportunities, and our AI analysis platform."
        keywords={[
          "startup FAQ",
          "business idea questions",
          "ideahunter help",
          "startup validation FAQ",
          "reddit startup ideas FAQ",
          "AI startup analysis"
        ]}
        type="website"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": filteredFAQs.map(faq => ({
            "@type": "Question",
            "name": faq.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": faq.answer
            }
          }))
        }}
      />
      
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <Button
                onClick={() => setLocation('/')}
                variant="ghost"
                className="mb-6 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>

              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <HelpCircle className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                    Frequently Asked Questions
                  </h1>
                  <p className="text-xl text-gray-300">
                    Everything you need to know about IdeaHunter
                  </p>
                </div>
              </div>

              <p className="text-lg text-gray-300 leading-relaxed">
                Find answers to common questions about our platform, startup idea validation, 
                and how to make the most of Reddit-sourced business opportunities.
              </p>
            </motion.div>

            {/* Search and Filter */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Search */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder="Search questions..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 bg-white/5 border-white/20 text-white placeholder-gray-400"
                      />
                    </div>

                    {/* Category Filter */}
                    <div className="flex flex-wrap gap-2">
                      {CATEGORIES.map((category) => (
                        <Button
                          key={category}
                          variant={selectedCategory === category ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedCategory(category)}
                          className={`${
                            selectedCategory === category
                              ? "bg-blue-500 hover:bg-blue-600 text-white"
                              : "bg-transparent border-white/20 text-gray-300 hover:bg-white/10"
                          }`}
                        >
                          {category}
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* FAQ Items */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-4"
            >
              {filteredFAQs.length === 0 ? (
                <Card className="glass-card border-white/10">
                  <CardContent className="p-8 text-center">
                    <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">No questions found</h3>
                    <p className="text-gray-400">
                      Try adjusting your search terms or category filter.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                filteredFAQs.map((faq, index) => (
                  <motion.div
                    key={faq.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 * index }}
                  >
                    <Card className="glass-card border-white/10 hover:border-white/20 transition-all duration-200">
                      <CardHeader 
                        className="cursor-pointer"
                        onClick={() => toggleExpanded(faq.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge className="bg-blue-500/20 text-blue-400 text-xs">
                                {faq.category}
                              </Badge>
                            </div>
                            <CardTitle className="text-white text-lg leading-relaxed">
                              {faq.question}
                            </CardTitle>
                          </div>
                          <div className="flex-shrink-0 ml-4">
                            {expandedItems.includes(faq.id) ? (
                              <ChevronUp className="w-5 h-5 text-gray-400" />
                            ) : (
                              <ChevronDown className="w-5 h-5 text-gray-400" />
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      
                      {expandedItems.includes(faq.id) && (
                        <CardContent className="pt-0">
                          <div className="border-t border-white/10 pt-4">
                            <p className="text-gray-300 leading-relaxed">
                              {faq.answer}
                            </p>
                            {faq.keywords.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-4">
                                {faq.keywords.map((keyword, idx) => (
                                  <Badge 
                                    key={idx}
                                    className="bg-gray-700/50 text-gray-400 text-xs"
                                  >
                                    {keyword}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  </motion.div>
                ))
              )}
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-12"
            >
              <Card className="glass-card border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Lightbulb className="w-5 h-5 mr-2 text-yellow-400" />
                    Popular Resources
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button
                      variant="outline"
                      className="justify-start h-auto p-4 bg-transparent border-white/20 text-left hover:bg-white/10"
                      onClick={() => setLocation('/faq/how-to-validate-startup-ideas')}
                    >
                      <div>
                        <div className="font-semibold text-white mb-1">Startup Validation Guide</div>
                        <div className="text-sm text-gray-400">Learn how to validate your startup ideas</div>
                      </div>
                    </Button>
                    
                    <Button
                      variant="outline"
                      className="justify-start h-auto p-4 bg-transparent border-white/20 text-left hover:bg-white/10"
                      onClick={() => setLocation('/best/startup-ideas-2025')}
                    >
                      <div>
                        <div className="font-semibold text-white mb-1">Best Startup Ideas 2025</div>
                        <div className="text-sm text-gray-400">Top-ranked business opportunities</div>
                      </div>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Extended Reading */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mt-12"
            >
              <Card className="glass-card border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <BookOpen className="w-5 h-5 mr-2 text-blue-400" />
                    扩展阅读
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="text-lg font-semibold text-white">深度指南</h4>
                      <div className="space-y-3">
                        <a
                          href="/blog/complete-guide-finding-startup-ideas-reddit-2025"
                          className="block p-3 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200 hover:bg-white/5"
                        >
                          <div className="font-medium text-white mb-1">Complete Guide to Finding Startup Ideas</div>
                          <div className="text-sm text-gray-400">Learn our proven methodology for discovering opportunities</div>
                        </a>

                        <a
                          href="/blog/reddit-vs-traditional-market-research-startup-ideas"
                          className="block p-3 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200 hover:bg-white/5"
                        >
                          <div className="font-medium text-white mb-1">Reddit vs Traditional Market Research</div>
                          <div className="text-sm text-gray-400">Compare different research approaches and their effectiveness</div>
                        </a>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="text-lg font-semibold text-white">成功案例</h4>
                      <div className="space-y-3">
                        <a
                          href="/blog/reddit-comment-to-million-dollar-business-success-stories"
                          className="block p-3 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200 hover:bg-white/5"
                        >
                          <div className="font-medium text-white mb-1">From Reddit Comment to $1M Business</div>
                          <div className="text-sm text-gray-400">Real stories of entrepreneurs who succeeded</div>
                        </a>

                        <a
                          href="/blog/analyzed-10000-reddit-posts-top-50-startup-ideas-2025"
                          className="block p-3 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200 hover:bg-white/5"
                        >
                          <div className="font-medium text-white mb-1">Top 50 Startup Ideas for 2025</div>
                          <div className="text-sm text-gray-400">Data-driven analysis of the best opportunities</div>
                        </a>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

          </div>
        </div>
      </div>
    </>
  );
}
