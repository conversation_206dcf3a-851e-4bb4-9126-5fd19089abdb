import { useState } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, TrendingUp, Calendar, BarChart3, Target, Zap, ArrowUp, ArrowDown, Minus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/hooks/use-auth.tsx";
import { useIdeas } from "@/hooks/use-ideas";
import { useIndustries } from "@/hooks/use-industries";
import SEOHead from "@/components/seo-head";
import ParticleBackground from "@/components/particle-background";
import IdeaGrid from "@/components/idea-grid";

interface TrendData {
  industry: string;
  industrySlug: string;
  totalIdeas: number;
  avgUpvotes: number;
  growth: number;
  topKeywords: string[];
  color: string;
}

interface MonthlyTrend {
  month: string;
  totalIdeas: number;
  topIndustries: string[];
}

export default function Trends() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { user } = useAuth();

  const year = params.year || "2025";

  // Get all industries and ideas for trend analysis
  const { data: industries } = useIndustries();
  const { data: allIdeas } = useIdeas({ pageSize: 1000 });

  // Calculate trend data
  const { data: trendData, isLoading } = useQuery({
    queryKey: ['trends', year, industries, allIdeas],
    queryFn: async () => {
      if (!industries || !allIdeas?.ideas) return null;

      // Get industry trends
      const industryTrends: TrendData[] = [];

      for (const industry of industries) {
        const industryIdeas = allIdeas.ideas.filter(idea => idea.industryId === industry.id);
        
        if (industryIdeas.length === 0) continue;

        const avgUpvotes = Math.round(
          industryIdeas.reduce((sum, idea) => sum + (idea.upvotes || 0), 0) / industryIdeas.length
        );

        // Calculate growth (mock data for demo - in real app, compare with previous period)
        const growth = Math.floor(Math.random() * 40) - 10; // -10% to +30%

        // Get top keywords
        const allKeywords = industryIdeas.flatMap(idea => idea.keywords || []);
        const keywordCounts = allKeywords.reduce((acc, keyword) => {
          acc[keyword] = (acc[keyword] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        const topKeywords = Object.entries(keywordCounts)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3)
          .map(([keyword]) => keyword);

        industryTrends.push({
          industry: industry.name,
          industrySlug: industry.slug,
          totalIdeas: industryIdeas.length,
          avgUpvotes,
          growth,
          topKeywords,
          color: industry.color
        });
      }

      // Sort by total ideas descending
      industryTrends.sort((a, b) => b.totalIdeas - a.totalIdeas);

      // Generate monthly trends (mock data for demo)
      const monthlyTrends: MonthlyTrend[] = [
        { month: "Jan 2025", totalIdeas: 45, topIndustries: ["AI & ML", "FinTech", "SaaS"] },
        { month: "Feb 2025", totalIdeas: 52, topIndustries: ["AI & ML", "EdTech", "HealthTech"] },
        { month: "Mar 2025", totalIdeas: 48, topIndustries: ["SaaS", "AI & ML", "E-commerce"] },
        { month: "Apr 2025", totalIdeas: 61, topIndustries: ["AI & ML", "FinTech", "Gaming"] },
        { month: "May 2025", totalIdeas: 58, topIndustries: ["HealthTech", "AI & ML", "SaaS"] },
        { month: "Jun 2025", totalIdeas: 67, topIndustries: ["AI & ML", "SaaS", "FinTech"] }
      ];

      return {
        industryTrends: industryTrends.slice(0, 10),
        monthlyTrends,
        totalIdeas: allIdeas.ideas.length,
        totalIndustries: industries.length,
        avgUpvotes: Math.round(
          allIdeas.ideas.reduce((sum, idea) => sum + (idea.upvotes || 0), 0) / allIdeas.ideas.length
        )
      };
    },
    enabled: !!industries && !!allIdeas?.ideas
  });

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <ArrowUp className="w-4 h-4 text-green-400" />;
    if (growth < 0) return <ArrowDown className="w-4 h-4 text-red-400" />;
    return <Minus className="w-4 h-4 text-gray-400" />;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return "text-green-400";
    if (growth < 0) return "text-red-400";
    return "text-gray-400";
  };

  if (isLoading || !trendData) {
    return (
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative min-h-screen">
        <ParticleBackground />
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto space-y-8">
            <Skeleton className="h-12 w-3/4 bg-white/10" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Skeleton className="h-32 bg-white/10" />
              <Skeleton className="h-32 bg-white/10" />
              <Skeleton className="h-32 bg-white/10" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHead 
        title={`Startup Trends ${year} | IdeaHunter`}
        description={`Discover the hottest startup trends and business opportunities for ${year}. Analyze industry growth, trending keywords, and emerging market opportunities from Reddit communities.`}
        keywords={[
          `startup trends ${year}`,
          "business trends",
          "emerging opportunities",
          "industry analysis",
          "market trends",
          "startup opportunities",
          "business intelligence"
        ]}
        type="website"
      />
      
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative min-h-screen">
        <ParticleBackground />

        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <Button
                onClick={() => setLocation('/')}
                variant="ghost"
                className="mb-6 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>

              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 rounded-xl bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                    Startup Trends {year}
                  </h1>
                  <p className="text-xl text-gray-300">
                    Industry Analysis & Market Opportunities
                  </p>
                </div>
              </div>

              <p className="text-lg text-gray-300 leading-relaxed">
                Discover the hottest startup trends and emerging business opportunities. 
                Our AI analyzes thousands of Reddit discussions to identify growing markets and trending ideas.
              </p>
            </motion.div>

            {/* Overview Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <BarChart3 className="w-8 h-8 text-blue-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {trendData.totalIdeas}
                      </p>
                      <p className="text-gray-400">Total Ideas Analyzed</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Target className="w-8 h-8 text-purple-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {trendData.totalIndustries}
                      </p>
                      <p className="text-gray-400">Industries Covered</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <TrendingUp className="w-8 h-8 text-green-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {trendData.avgUpvotes}
                      </p>
                      <p className="text-gray-400">Avg Community Score</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Industry Trends */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-orange-400" />
                    Industry Trends & Growth
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {trendData.industryTrends.map((trend, index) => {
                      return (
                        <motion.div
                          key={trend.industrySlug}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * index }}
                          className="flex items-center justify-between p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer"
                          onClick={() => setLocation(`/industry/${trend.industrySlug}`)}
                        >
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                              <span className="text-2xl font-bold text-gray-400">#{index + 1}</span>
                              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                            </div>
                            <div>
                              <h3 className="text-white font-semibold">{trend.industry}</h3>
                              <div className="flex items-center space-x-4 text-sm text-gray-400">
                                <span>{trend.totalIdeas} ideas</span>
                                <span>{trend.avgUpvotes} avg upvotes</span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-4">
                            {/* Keywords */}
                            <div className="hidden md:flex space-x-1">
                              {trend.topKeywords.slice(0, 2).map((keyword, idx) => (
                                <Badge key={idx} className="bg-gray-700/50 text-gray-300 text-xs">
                                  {keyword}
                                </Badge>
                              ))}
                            </div>
                            
                            {/* Growth */}
                            <div className={`flex items-center space-x-1 ${getGrowthColor(trend.growth)}`}>
                              {getGrowthIcon(trend.growth)}
                              <span className="font-semibold">
                                {trend.growth > 0 ? '+' : ''}{trend.growth}%
                              </span>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Monthly Trends */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Calendar className="w-5 h-5 mr-2 text-blue-400" />
                    Monthly Activity Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {trendData.monthlyTrends.map((month, index) => (
                      <motion.div
                        key={month.month}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * index }}
                        className="p-4 rounded-lg bg-white/5 border border-white/10"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="text-white font-semibold">{month.month}</h4>
                          <Badge className="bg-blue-500/20 text-blue-400">
                            {month.totalIdeas} ideas
                          </Badge>
                        </div>
                        <div>
                          <p className="text-gray-400 text-sm mb-2">Top Industries:</p>
                          <div className="space-y-1">
                            {month.topIndustries.map((industry, idx) => (
                              <div key={idx} className="flex items-center space-x-2">
                                <span className="w-1.5 h-1.5 bg-purple-400 rounded-full"></span>
                                <span className="text-gray-300 text-sm">{industry}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Trending Ideas */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mb-8"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white flex items-center">
                  <Zap className="w-6 h-6 mr-2 text-yellow-400" />
                  Trending Ideas This Month
                </h2>
                {!user && (
                  <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                    Sign in to see all trending ideas
                  </Badge>
                )}
              </div>

              <IdeaGrid
                ideas={allIdeas?.ideas?.slice(0, 12) || []}
                isLoading={false}
                isLimited={allIdeas?.isLimited}
                useNavigation={true}
                isFetching={false}
              />
            </motion.div>

          </div>
        </div>
      </div>
    </>
  );
}
